#!/bin/bash

# 日志权限修复脚本
# 解决root用户创建的日志文件导致www用户无法访问的问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log_info "开始修复日志权限问题..."
log_info "项目目录: $PROJECT_DIR"

# 检查是否有sudo权限
if [ "$EUID" -ne 0 ]; then
    log_error "此脚本需要sudo权限运行"
    echo "请使用: sudo $0"
    exit 1
fi

# 修复runtime目录权限
log_info "修复runtime目录权限..."
if [ -d "$PROJECT_DIR/runtime" ]; then
    # 修改所有者为www-data
    chown -R www-data:www-data "$PROJECT_DIR/runtime"
    
    # 设置目录权限为755，文件权限为644
    find "$PROJECT_DIR/runtime" -type d -exec chmod 755 {} \;
    find "$PROJECT_DIR/runtime" -type f -exec chmod 644 {} \;
    
    # 确保日志目录可写
    chmod -R 755 "$PROJECT_DIR/runtime/log" 2>/dev/null || true
    chmod -R 755 "$PROJECT_DIR/runtime/admin/log" 2>/dev/null || true
    
    log_success "runtime目录权限修复完成"
else
    log_warning "runtime目录不存在，跳过"
fi

# 修复public/uploads目录权限
log_info "修复uploads目录权限..."
if [ -d "$PROJECT_DIR/public/uploads" ]; then
    chown -R www-data:www-data "$PROJECT_DIR/public/uploads"
    chmod -R 755 "$PROJECT_DIR/public/uploads"
    log_success "uploads目录权限修复完成"
else
    log_warning "uploads目录不存在，跳过"
fi

# 检查并显示当前权限状态
log_info "检查修复后的权限状态..."
if [ -d "$PROJECT_DIR/runtime/log" ]; then
    echo "runtime/log 目录权限:"
    ls -la "$PROJECT_DIR/runtime/log"
fi

# 创建权限检查脚本
log_info "创建权限检查脚本..."
cat > "$PROJECT_DIR/scripts/check_permissions.sh" << 'EOF'
#!/bin/bash
# 权限检查脚本

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "检查关键目录权限:"
echo "===================="

if [ -d "$PROJECT_DIR/runtime" ]; then
    echo "runtime目录:"
    ls -ld "$PROJECT_DIR/runtime"
    
    if [ -d "$PROJECT_DIR/runtime/log" ]; then
        echo "runtime/log目录:"
        ls -ld "$PROJECT_DIR/runtime/log"
        
        echo "最近的日志文件:"
        find "$PROJECT_DIR/runtime/log" -name "*.log" -type f -exec ls -la {} \; | head -5
    fi
fi

if [ -d "$PROJECT_DIR/public/uploads" ]; then
    echo "uploads目录:"
    ls -ld "$PROJECT_DIR/public/uploads"
fi

echo ""
echo "如果发现权限问题，请运行: sudo ./scripts/fix_log_permissions.sh"
EOF

chmod +x "$PROJECT_DIR/scripts/check_permissions.sh"
log_success "权限检查脚本创建完成: scripts/check_permissions.sh"

log_success "日志权限修复完成！"
log_info "建议定期运行权限检查: ./scripts/check_permissions.sh"
