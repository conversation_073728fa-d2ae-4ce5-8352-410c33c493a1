---
type: "always_apply"
---

# AI 协作开发规则

## 1. 总体原则

1.1. **遵守现有规范**: 严格遵守项目中已有的代码风格、命名规范和设计模式。在修改或添加代码前，先阅读相关模块的现有代码。
1.2. **代码质量优先**: 优先考虑代码的可读性、可维护性和健壮性。禁止提交未经测试或存在明显问题的代码。
1.3. **明确意图**: 所有代码、注释和提交信息都应清晰地表达其意图。避免使用模糊或模棱两可的描述。
1.4. **小步提交**: 每次提交应只包含一个逻辑上完整的变更。避免将多个不相关的修改混在同一次提交中。

## 2. 文件和目录规范

2.1. **命名**:
    - 类文件使用大驼峰命名法 (PascalCase)，例如 `GoodsValidate.php`。
    - 函数和方法使用小驼峰命名法 (camelCase)，例如 `checkActivityGoods`。
    - 变量使用下划线命名法 (snake_case)，例如 `$goods_id`。
    - 配置文件使用小写字母和下划线，例如 `database.php`。
2.2. **目录结构**:
    - 新功能应根据 MVC 结构放置在对应的 `app` 目录下的 `controller`, `model`, `validate`, `logic` 等目录中。
    - 非业务相关的脚本文件应放置在 `scripts` 目录下。
    - 文档应放置在 `docs` 目录下。

## 3. PHP 代码规范

3.1. **框架**: 项目基于 ThinkPHP 框架，所有代码必须遵循 ThinkPHP 的开发规范。
3.2. **命名空间**: 所有类都必须定义在正确的命名空间下，例如 `namespace app\shop\validate\goods;`。
3.3. **注释**:
    - 每个类和公共方法都必须有 PHPDoc 注释，说明其功能、参数和返回值。
    - 复杂或不直观的代码块应添加行内注释。
3.4. **验证**:
    - 任何来自用户输入或客户端的数据都必须经过严格的验证。
    - 验证逻辑应写在 `validate` 类中，保持控制器（Controller）和逻辑层（Logic）的简洁。
3.5. **数据库操作**:
    - 严禁在代码中拼接 SQL 语句，必须使用框架提供的查询构造器或模型来操作数据库，以防止 SQL 注入。
    - 复杂的数据库查询应封装在 `model` 或 `logic` 层中。
3.6. **错误和异常**:
    - 使用 `try-catch` 块来处理可能抛出异常的代码。
    - 返回给客户端的错误信息应友好且不暴露系统内部细节。

## 4. 安全规范

4.1. **SQL 注入**: 严格遵守第 3.5 条，杜绝 SQL 注入风险。
4.2. **跨站脚本 (XSS)**: 对输出到 HTML 的数据进行转义，特别是用户提交的内容。
4.3. **权限控制**: 严格校验用户权限，确保用户只能访问其被授权的资源。
4.4. **敏感信息**: 不得在代码或日志中硬编码密码、API 密钥等敏感信息。应使用配置文件或环境变量进行管理。

## 5. Git 使用规范

5.1. **分支管理**:
    - `master` 分支为稳定分支，只能合并 `develop` 分支的代码。
    - `develop` 分支为开发分支。
    - 新功能或修复 bug 应从 `develop` 分支创建新的 `feature` 或 `fix` 分支。
5.2. **提交信息 (Commit Message)**:
    - 格式: `<type>: <subject>`
    - `type`: `feat` (新功能), `fix` (修复 bug), `docs` (文档), `style` (格式), `refactor` (重构), `test` (测试), `chore` (构建或辅助工具)。
    - `subject`: 简明扼要地描述本次提交的内容。
    - 示例: `feat: add goods validation rules`

## 6. AI 协作特定规则

6.1. **明确任务**: 在提出需求时，请提供清晰、具体的指令，包括上下文和预期输出。
6.2. **提供示例**: 如果需要遵循特定的代码风格或格式，请提供一个代码示例。
6.3. **审查和测试**: AI 生成的代码必须经过人工审查和充分测试后才能合并到主干分支。AI 作为辅助工具，最终责任由开发者承担。
6.4. **增量修改**: 对于现有文件的修改，优先使用 `search_and_replace` 或 `insert_content` 工具进行精确修改，而不是覆盖整个文件。

## 7. MCP 服务使用指南

为了更高效地与 AI 协作，请遵循以下 MCP 服务使用指南：

7.1. **文件系统操作 (`filesystem`)**:
    - **读取文件**: 使用 `read_file` 查看文件内容。
    - **写入文件**: 使用 `write_to_file` 创建新文件或完全覆盖现有文件。**注意：** 此操作是破坏性的，请谨慎使用。
    - **编辑文件**: 对于小型、精确的修改，应优先使用 `edit_file` (通过 `search_and_replace` 或 `insert_content`)。这比覆盖整个文件更安全、高效。
    - **文件和目录管理**: 使用 `list_directory`, `create_directory`, `move_file` 等工具来管理项目结构。

7.2. **复杂任务处理 (`sequential-thinking`)**:
    - 当面临复杂问题（例如，需要多步骤分析、设计或重构的开发任务）时，应启用 `sequential-thinking` MCP 服务。
    - **逐步拆解**: 利用此工具将复杂任务分解为一系列更小、更易于管理和验证的步骤。
    - **过程透明**: AI 将展示其思考过程，允许您在早期阶段发现潜在问题并进行干预。
    - **假设验证**: 在执行每个步骤之前，AI 会提出假设并寻求您的确认，确保最终解决方案符合您的预期。

7.3. **知识图谱与记忆 (`memory`)**:
    - 对于需要跨越多个会话或任务的上下文信息，可以使用 `memory` 服务来创建实体和关系。
    - 这有助于 AI 更好地理解项目的宏观结构和长期目标。

7.4. **其他 MCP 服务**:
    - 根据具体任务，可以灵活使用 `playwright` (用于浏览器自动化测试), `ssh-mpc-server` (用于远程服务器操作) 等其他可用的 MCP 服务。
    - 在使用任何 MCP 服务之前，请确保已理解其功能和潜在影响。