#!/bin/bash

# 定时任务权限设置脚本
# 确保所有定时任务以正确的用户权限运行

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log_info "设置定时任务权限..."
log_info "项目目录: $PROJECT_DIR"

# 检查当前用户
CURRENT_USER=$(whoami)
log_info "当前用户: $CURRENT_USER"

# 检查是否存在www-data用户
if id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    WEB_USER="www"
fi

log_info "检测到的Web用户: $WEB_USER"

# 检查现有的crontab
log_info "检查现有的crontab配置..."
echo "Root用户的crontab:"
sudo crontab -l 2>/dev/null | grep -E "(php|think)" || echo "未找到相关定时任务"

echo ""
echo "$WEB_USER 用户的crontab:"
sudo crontab -u $WEB_USER -l 2>/dev/null | grep -E "(php|think)" || echo "未找到相关定时任务"

# 创建示例crontab配置
log_info "创建示例crontab配置文件..."
cat > "$PROJECT_DIR/scripts/crontab_example.txt" << EOF
# 示例crontab配置 - 以www-data用户运行
# 请根据实际需要修改并使用以下命令安装：
# sudo crontab -u www-data crontab_example.txt

# 每分钟执行定时任务
* * * * * cd $PROJECT_DIR && /usr/bin/php think crontab >> /var/log/crontab.log 2>&1

# 每5分钟同步商品到搜索引擎
*/5 * * * * cd $PROJECT_DIR && /usr/bin/php think sync_goods_to_meilisearch >> /var/log/sync_goods.log 2>&1

# 每小时执行数据统计
0 * * * * cd $PROJECT_DIR && /usr/bin/php think stat_daily >> /var/log/stat_daily.log 2>&1

# 每天凌晨清理过期数据
0 2 * * * cd $PROJECT_DIR && /usr/bin/php think order_close >> /var/log/order_close.log 2>&1
EOF

log_success "示例crontab配置已创建: scripts/crontab_example.txt"

# 创建crontab安装脚本
cat > "$PROJECT_DIR/scripts/install_crontab.sh" << 'EOF'
#!/bin/bash

# crontab安装脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 检测Web用户
if id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    WEB_USER="www"
fi

echo "将为用户 $WEB_USER 安装crontab..."
echo "配置文件: $SCRIPT_DIR/crontab_example.txt"
echo ""

read -p "确认安装？(y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "$SCRIPT_DIR/crontab_example.txt" ]; then
        sudo crontab -u $WEB_USER "$SCRIPT_DIR/crontab_example.txt"
        echo "crontab安装完成！"
        echo ""
        echo "查看已安装的crontab:"
        sudo crontab -u $WEB_USER -l
    else
        echo "错误: 配置文件不存在"
        exit 1
    fi
else
    echo "安装已取消"
fi
EOF

chmod +x "$PROJECT_DIR/scripts/install_crontab.sh"
log_success "crontab安装脚本已创建: scripts/install_crontab.sh"

# 创建权限监控脚本
cat > "$PROJECT_DIR/scripts/monitor_permissions.sh" << 'EOF'
#!/bin/bash

# 权限监控脚本 - 定期检查和修复权限问题

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 检测Web用户
if id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    WEB_USER="www"
fi

LOG_FILE="/var/log/permission_monitor.log"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 检查runtime目录权限
check_runtime_permissions() {
    if [ -d "$PROJECT_DIR/runtime" ]; then
        # 查找不属于web用户的文件
        WRONG_OWNER_FILES=$(find "$PROJECT_DIR/runtime" ! -user $WEB_USER -type f 2>/dev/null)
        
        if [ -n "$WRONG_OWNER_FILES" ]; then
            log_message "发现权限问题，正在修复..."
            chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR/runtime"
            chmod -R 755 "$PROJECT_DIR/runtime"
            log_message "权限修复完成"
        fi
    fi
}

# 检查uploads目录权限
check_uploads_permissions() {
    if [ -d "$PROJECT_DIR/public/uploads" ]; then
        WRONG_OWNER_FILES=$(find "$PROJECT_DIR/public/uploads" ! -user $WEB_USER -type f 2>/dev/null)
        
        if [ -n "$WRONG_OWNER_FILES" ]; then
            log_message "发现uploads权限问题，正在修复..."
            chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR/public/uploads"
            chmod -R 755 "$PROJECT_DIR/public/uploads"
            log_message "uploads权限修复完成"
        fi
    fi
}

# 执行检查
log_message "开始权限检查..."
check_runtime_permissions
check_uploads_permissions
log_message "权限检查完成"
EOF

chmod +x "$PROJECT_DIR/scripts/monitor_permissions.sh"
log_success "权限监控脚本已创建: scripts/monitor_permissions.sh"

# 提供使用说明
echo ""
log_info "使用说明:"
echo "1. 查看示例配置: cat scripts/crontab_example.txt"
echo "2. 安装crontab: ./scripts/install_crontab.sh"
echo "3. 检查权限: ./scripts/check_permissions.sh"
echo "4. 修复权限: sudo ./scripts/fix_log_permissions.sh"
echo "5. 监控权限: ./scripts/monitor_permissions.sh"
echo ""
log_warning "重要提醒:"
echo "- 确保所有定时任务都以 $WEB_USER 用户运行"
echo "- 避免使用root用户执行php think命令"
echo "- 定期运行权限检查脚本"
