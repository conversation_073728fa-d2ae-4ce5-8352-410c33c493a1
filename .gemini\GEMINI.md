# 货行行项目分析与开发偏好

## 项目概述

**货行行**是一个基于ThinkPHP 6.0框架开发的B2B电商平台，主要面向商品批发和采购业务。项目采用多应用架构，支持商家入驻、商品管理、订单处理、用户活跃度系统等完整的电商功能。

## 技术架构分析

### 核心框架与依赖
- **后端框架**: ThinkPHP 6.0 + Think-ORM 2.0
- **PHP版本**: >= 7.4
- **数据库**: MySQL 5.7+
- **前端技术**: LayUI + jQuery + HTML5
- **支付集成**: 支付宝SDK、微信支付、易联云支付
- **云服务**: 阿里云OSS、腾讯云COS、七牛云存储
- **搜索引擎**: MeiliSearch
- **队列系统**: Think-Queue + Redis
- **WebSocket**: Workerman Gateway-Worker
### 用户开发喜好
**mcp** 喜好使用filesystem来编辑和创建和查看文件
**mcp** 喜欢使用playwright来自动化测试
**mcp** 喜欢使用browser-tools-mcp来自动化测试
**mcp** 喜欢使用ssh-mpc-server来自动化测试
**mcp** 喜欢使用context7来获取文档
**mcp** 喜欢使用sequential-thinking来思考复杂任务
### 应用模块结构
```
app/
├── admin/          # 后台管理系统
├── api/            # 用户端API接口
├── shop/           # 商家端管理
├── shopapi/        # 商家端API接口
├── kefuapi/        # 客服系统API
├── index/          # 前台展示
├── common/         # 公共模块
├── model/          # 数据模型
├── command/        # 命令行工具
├── event/          # 事件处理
└── listener/       # 事件监听器
```

## 业务逻辑分析

### 核心业务模块

#### 1. 用户体系
- **多角色支持**: 普通用户、采购商、商家、代理商
- **活跃度系统**: 基于积分的用户等级体系(0-5级)
- **采购商标识**: 发布采购信息后自动标记
- **用户行为追踪**: 登录、聊天、购买等行为记录

#### 2. 商品管理
- **分类体系**: 支持多级商品分类
- **规格管理**: 商品多规格支持
- **库存管理**: 实时库存更新
- **图片预览**: 商品图片预览功能
- **搜索优化**: MeiliSearch全文搜索

#### 3. 订单系统
- **订单状态**: 待付款→待发货→待收货→已完成→已关闭
- **微信同步**: 微信小程序订单双向同步
- **退款处理**: 完整的售后退款流程
- **异步队列**: 订单取消等操作异步处理

#### 4. 商家管理
- **入驻审核**: 商家入驻申请审核流程
- **保证金**: 商家保证金管理和退款
- **等级配置**: 商家等级权限管理
- **数据统计**: 交易数据和商品数据统计

#### 5. 支付体系
- **多支付方式**: 支付宝、微信支付、银行卡
- **支付回调**: 完整的支付回调处理
- **退款处理**: 自动和手动退款支持

## 代码规范分析

### 1. 目录结构规范
- **MVC分层**: 严格按照Model-View-Controller分层
- **命名空间**: 遵循PSR-4自动加载规范
- **文件命名**: 驼峰命名法，类名与文件名一致

### 2. 类设计规范
```php
// 控制器基类继承
class XxxController extends AdminBase    // 后台控制器
class XxxController extends Api          // API控制器
class XxxController extends BaseController // 基础控制器

// 模型基类继承
class XxxModel extends Models           // 业务模型
class XxxModel extends Model           // 基础模型

// 逻辑层设计
class XxxLogic                         // 业务逻辑层
```

### 3. 数据库设计规范
- **表前缀**: 统一使用`ls_`前缀
- **字段命名**: 下划线命名法
- **时间字段**: `create_time`、`update_time`使用时间戳
- **软删除**: 使用`del`字段标记删除状态
- **索引设计**: 合理使用单列和复合索引

### 4. API接口规范
```php
// 统一返回格式
return $this->success('操作成功', $data);
return $this->fail('操作失败', $error);
return JsonServer::success('成功', $data);
return JsonServer::error('失败');
```

### 5. 异常处理规范
- **try-catch**: 关键业务逻辑使用异常捕获
- **日志记录**: 使用Log门面记录错误信息
- **用户友好**: 向用户返回友好的错误提示

## 开发偏好配置

### 编码风格偏好
```yaml
# PHP编码规范
php:
  standard: PSR-12
  indentation: 4_spaces
  line_length: 120
  array_syntax: short  # []而非array()
  
# 数据库规范
database:
  naming: snake_case
  prefix: ls_
  charset: utf8mb4
  engine: InnoDB
  
# 前端规范
frontend:
  framework: LayUI
  js_library: jQuery
  css_preprocessor: none
  indentation: 2_spaces
```

### 项目结构偏好
```yaml
# 新功能开发结构
feature_structure:
  controller: app/{module}/controller/
  logic: app/{module}/logic/
  model: app/common/model/
  validate: app/{module}/validate/
  view: app/{module}/view/
  
# API开发规范
api_structure:
  version: v1
  response_format: json
  authentication: token
  rate_limiting: enabled
```

### 业务开发偏好
```yaml
# 业务逻辑分层
business_layers:
  controller: 接收请求，参数验证，调用逻辑层
  logic: 业务逻辑处理，数据组装
  model: 数据访问，关联查询
  validate: 数据验证规则
  
# 数据处理偏好
data_handling:
  pagination: 使用ThinkPHP分页
  cache: Redis缓存热点数据
  queue: 异步处理耗时操作
  search: MeiliSearch全文搜索
```

### 安全规范偏好
```yaml
# 安全措施
security:
  input_validation: 严格验证所有输入
  sql_injection: 使用ORM防止SQL注入
  xss_protection: 输出转义处理
  csrf_protection: 表单CSRF令牌
  file_upload: 严格文件类型和大小限制
  
# 权限控制
permission:
  rbac: 基于角色的访问控制
  middleware: 使用中间件验证权限
  token: JWT或自定义Token认证
```

### 性能优化偏好
```yaml
# 性能优化策略
performance:
  database:
    - 合理使用索引
    - 避免N+1查询
    - 使用连接查询减少查询次数
  cache:
    - Redis缓存热点数据
    - 页面静态化
    - CDN加速静态资源
  queue:
    - 异步处理邮件发送
    - 异步处理订单状态更新
    - 异步处理数据统计
```

### 测试与部署偏好
```yaml
# 测试策略
testing:
  unit_test: PHPUnit单元测试
  api_test: Postman/Apifox接口测试
  browser_test: Playwright自动化测试
  
# 部署策略
deployment:
  environment: 开发/测试/生产环境分离
  version_control: Git版本控制
  ci_cd: 自动化部署流程
  monitoring: 日志监控和性能监控
```

## 开发建议

### 1. 新功能开发流程
1. **需求分析** → 明确业务需求和技术方案
2. **数据库设计** → 设计表结构和索引
3. **API设计** → 定义接口规范和数据格式
4. **编码实现** → 按照MVC分层开发
5. **测试验证** → 单元测试和集成测试
6. **文档编写** → API文档和部署文档

### 2. 代码质量保证
- **代码审查**: 提交前进行代码审查
- **单元测试**: 关键业务逻辑编写单元测试
- **性能测试**: 接口性能和数据库查询优化
- **安全测试**: 输入验证和权限控制测试

### 3. 维护和优化
- **定期重构**: 优化代码结构和性能
- **监控告警**: 系统性能和错误监控
- **文档更新**: 及时更新技术文档
- **版本管理**: 规范的版本发布流程

---

# AI 协作开发规则

## 1. 总体原则

1.1. **遵守现有规范**: 严格遵守项目中已有的代码风格、命名规范和设计模式。在修改或添加代码前，先阅读相关模块的现有代码。
1.2. **代码质量优先**: 优先考虑代码的可读性、可维护性和健壮性。禁止提交未经测试或存在明显问题的代码。
1.3. **明确意图**: 所有代码、注释和提交信息都应清晰地表达其意图。避免使用模糊或模棱两可的描述。
1.4. **小步提交**: 每次提交应只包含一个逻辑上完整的变更。避免将多个不相关的修改混在同一次提交中。

## 2. 文件和目录规范

2.1. **命名**:
    - 类文件使用大驼峰命名法 (PascalCase)，例如 `GoodsValidate.php`。
    - 函数和方法使用小驼峰命名法 (camelCase)，例如 `checkActivityGoods`。
    - 变量使用下划线命名法 (snake_case)，例如 `$goods_id`。
    - 配置文件使用小写字母和下划线，例如 `database.php`。
2.2. **目录结构**:
    - 新功能应根据 MVC 结构放置在对应的 `app` 目录下的 `controller`, `model`, `validate`, `logic` 等目录中。
    - 非业务相关的脚本文件应放置在 `scripts` 目录下。
    - 文档应放置在 `docs` 目录下。

## 3. PHP 代码规范

3.1. **框架**: 项目基于 ThinkPHP 框架，所有代码必须遵循 ThinkPHP 的开发规范。
3.2. **命名空间**: 所有类都必须定义在正确的命名空间下，例如 `namespace app\shop\validate\goods;`。
3.3. **注释**:
    - 每个类和公共方法都必须有 PHPDoc 注释，说明其功能、参数和返回值。
    - 复杂或不直观的代码块应添加行内注释。
3.4. **验证**:
    - 任何来自用户输入或客户端的数据都必须经过严格的验证。
    - 验证逻辑应写在 `validate` 类中，保持控制器（Controller）和逻辑层（Logic）的简洁。
3.5. **数据库操作**:
    - 严禁在代码中拼接 SQL 语句，必须使用框架提供的查询构造器或模型来操作数据库，以防止 SQL 注入。
    - 复杂的数据库查询应封装在 `model` 或 `logic` 层中。
3.6. **错误和异常**:
    - 使用 `try-catch` 块来处理可能抛出异常的代码。
    - 返回给客户端的错误信息应友好且不暴露系统内部细节。

## 4. 安全规范

4.1. **SQL 注入**: 严格遵守第 3.5 条，杜绝 SQL 注入风险。
4.2. **跨站脚本 (XSS)**: 对输出到 HTML 的数据进行转义，特别是用户提交的内容。
4.3. **权限控制**: 严格校验用户权限，确保用户只能访问其被授权的资源。
4.4. **敏感信息**: 不得在代码或日志中硬编码密码、API 密钥等敏感信息。应使用配置文件或环境变量进行管理。

## 5. Git 使用规范

5.1. **分支管理**:
    - `master` 分支为稳定分支，只能合并 `develop` 分支的代码。
    - `develop` 分支为开发分支。
    - 新功能或修复 bug 应从 `develop` 分支创建新的 `feature` 或 `fix` 分支。
5.2. **提交信息 (Commit Message)**:
    - 格式: `<type>: <subject>`
    - `type`: `feat` (新功能), `fix` (修复 bug), `docs` (文档), `style` (格式), `refactor` (重构), `test` (测试), `chore` (构建或辅助工具)。
    - `subject`: 简明扼要地描述本次提交的内容。
    - 示例: `feat: add goods validation rules`

## 6. AI 协作特定规则

6.1. **明确任务**: 在提出需求时，请提供清晰、具体的指令，包括上下文和预期输出。
6.2. **提供示例**: 如果需要遵循特定的代码风格或格式，请提供一个代码示例。
6.3. **审查和测试**: AI 生成的代码必须经过人工审查和充分测试后才能合并到主干分支。AI 作为辅助工具，最终责任由开发者承担。
6.4. **增量修改**: 对于现有文件的修改，优先使用 `search_and_replace` 或 `insert_content` 工具进行精确修改，而不是覆盖整个文件。

## 7. MCP 服务使用指南

为了更高效地与 AI 协作，请遵循以下 MCP 服务使用指南：

7.1. **文件系统操作 (`filesystem`)**:
    - **读取文件**: 使用 `read_file` 查看文件内容。
    - **写入文件**: 使用 `write_to_file` 创建新文件或完全覆盖现有文件。**注意：** 此操作是破坏性的，请谨慎使用。
    - **编辑文件**: 对于小型、精确的修改，应优先使用 `edit_file` (通过 `search_and_replace` 或 `insert_content`)。这比覆盖整个文件更安全、高效。
    - **文件和目录管理**: 使用 `list_directory`, `create_directory`, `move_file` 等工具来管理项目结构。

7.2. **复杂任务处理 (`sequential-thinking`)**:
    - 当面临复杂问题（例如，需要多步骤分析、设计或重构的开发任务）时，应启用 `sequential-thinking` MCP 服务。
    - **逐步拆解**: 利用此工具将复杂任务分解为一系列更小、更易于管理和验证的步骤。
    - **过程透明**: AI 将展示其思考过程，允许您在早期阶段发现潜在问题并进行干预。
    - **假设验证**: 在执行每个步骤之前，AI 会提出假设并寻求您的确认，确保最终解决方案符合您的预期。

7.3. **知识图谱与记忆 (`memory`)**:
    - 对于需要跨越多个会话或任务的上下文信息，可以使用 `memory` 服务来创建实体和关系。
    - 这有助于 AI 更好地理解项目的宏观结构和长期目标。

7.4. **其他 MCP 服务**:
    - 根据具体任务，可以灵活使用 `playwright` (用于浏览器自动化测试), `ssh-mpc-server` (用于远程服务器操作) 等其他可用的 MCP 服务。
    - 在使用任何 MCP 服务之前，请确保已理解其功能和潜在影响。